# OpenAI Embeddings 向量化工具

基于 `langchain_openai.OpenAIEmbeddings` 的文本向量化工具集，提供简单易用的API和高级功能。

## 文件结构

```
rag/
├── simple_vectorizer.py      # 简单向量化器
├── enhanced_vectorizer.py    # 增强版向量化器
├── vectorizer_config.py      # 配置管理
├── vectorizer_example.py     # 使用示例
└── README_vectorizer.md      # 说明文档
```

## 功能特性

### SimpleVectorizer (简单向量化器)
- ✅ 基于 OpenAI Embeddings API
- ✅ 支持单个文本和批量文档向量化
- ✅ 自动类型检测和错误处理
- ✅ 简洁的API设计

### EnhancedVectorizer (增强版向量化器)
- ✅ 所有简单版功能
- ✅ 批处理支持，可配置批次大小
- ✅ 重试机制和指数退避
- ✅ 进度条显示
- ✅ 回调函数支持
- ✅ 详细的日志记录

### VectorizerConfig (配置管理)
- ✅ 多种配置方式（环境变量、字典、预设）
- ✅ 支持多个API提供商
- ✅ 配置验证和模型信息

## 快速开始

### 1. 安装依赖

```bash
pip install langchain-openai tqdm
```

### 2. 设置API密钥

```bash
# 方式1: 环境变量
export OPENAI_API_KEY="your-api-key-here"

# 方式2: 代码中直接设置（见下面示例）
```

### 3. 基本使用

```python
from rag.simple_vectorizer import SimpleVectorizer

# 初始化（使用环境变量）
vectorizer = SimpleVectorizer()

# 或者直接提供API密钥
vectorizer = SimpleVectorizer(
    api_key="your-api-key-here",
    model="text-embedding-3-small"
)

# 单个文本向量化
text = "人工智能正在改变世界"
vector = vectorizer.vectorize_text(text)
print(f"向量维度: {len(vector)}")

# 批量文档向量化
documents = [
    "机器学习是AI的核心",
    "深度学习推动了AI发展"
]
vectors = vectorizer.vectorize_documents(documents)
print(f"向量数量: {len(vectors)}")

# 通用方法（自动判断类型）
single_vector = vectorizer.vectorize("单个文本")
batch_vectors = vectorizer.vectorize(["文本1", "文本2"])
```

### 4. 高级功能

```python
from rag.enhanced_vectorizer import EnhancedVectorizer
from rag.vectorizer_config import VectorizerConfig

# 创建配置
config = VectorizerConfig.from_env()
vectorizer = EnhancedVectorizer(config)

# 批量向量化（带进度条和重试）
documents = ["文档1", "文档2", "文档3", "文档4"]
vectors = vectorizer.vectorize_documents_batch(
    documents,
    batch_size=2,
    show_progress=True,
    max_retries=3
)

# 带回调函数的向量化
def process_batch(batch_index, batch_vectors):
    print(f"处理完第{batch_index + 1}批")

vectors = vectorizer.vectorize_with_callback(
    documents,
    callback=process_batch,
    batch_size=2
)
```

## 配置选项

### 支持的模型

| 模型名称 | 维度 | 说明 |
|---------|------|------|
| text-embedding-3-small | 1536 | 最新小模型，性价比高 |
| text-embedding-3-large | 3072 | 最新大模型，效果最好 |
| text-embedding-ada-002 | 1536 | 经典模型 |

### API提供商配置

```python
from rag.vectorizer_config import get_openai_config, get_siliconflow_config

# OpenAI官方
config = get_openai_config("your-openai-key", "text-embedding-3-small")

# SiliconFlow
config = get_siliconflow_config("your-silicon-key")

# 自定义配置
config = VectorizerConfig.from_dict({
    "api_key": "your-key",
    "model": "text-embedding-3-large",
    "base_url": "https://custom-api.com/v1",
    "batch_size": 100,
    "timeout": 60
})
```

### 环境变量

```bash
OPENAI_API_KEY=your-api-key-here
EMBEDDING_MODEL=text-embedding-3-small
OPENAI_BASE_URL=https://api.openai.com/v1
EMBEDDING_BATCH_SIZE=100
EMBEDDING_TIMEOUT=60
```

## 错误处理

工具提供了完善的错误处理机制：

```python
try:
    vectorizer = SimpleVectorizer()
    vector = vectorizer.vectorize_text("测试文本")
except ValueError as e:
    print(f"输入错误: {e}")
except Exception as e:
    print(f"API调用错误: {e}")
```

## 性能优化建议

1. **批处理大小**: 根据API限制调整 `batch_size`，通常 50-100 较合适
2. **重试机制**: 网络不稳定时启用重试，避免单次失败
3. **并发控制**: 避免过高的并发请求，遵守API速率限制
4. **缓存结果**: 对于重复文本，考虑缓存向量结果

## 运行示例

```bash
cd rag
python vectorizer_example.py
```

## 注意事项

1. **API密钥安全**: 不要在代码中硬编码API密钥，使用环境变量
2. **速率限制**: 遵守API提供商的速率限制
3. **成本控制**: 向量化会产生API调用费用，注意控制使用量
4. **模型选择**: 根据精度和成本需求选择合适的模型

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   解决: 检查API密钥是否正确设置
   ```

2. **网络连接问题**
   ```
   解决: 检查网络连接，考虑使用重试机制
   ```

3. **模型不支持**
   ```
   解决: 检查模型名称是否正确，参考支持的模型列表
   ```

4. **批处理失败**
   ```
   解决: 减小batch_size，检查单个文档是否过长
   ```

## 扩展开发

如需扩展功能，可以：

1. 继承 `SimpleVectorizer` 或 `EnhancedVectorizer`
2. 修改 `VectorizerConfig` 添加新的配置选项
3. 实现自定义的错误处理和重试逻辑

## 许可证

本工具基于开源许可证，请遵守相关条款。
