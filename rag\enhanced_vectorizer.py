"""
增强版OpenAI Embeddings向量化工具
支持批处理、错误重试、进度显示等功能
"""

from langchain_openai import OpenAIEmbeddings
from typing import List, Union, Optional, Callable
import time
import logging
from tqdm import tqdm
from .vectorizer_config import VectorizerConfig


class EnhancedVectorizer:
    """
    增强版向量化器
    支持批处理、重试机制、进度显示等高级功能
    """
    
    def __init__(self, config: VectorizerConfig):
        """
        初始化增强版向量化器
        
        Args:
            config: 向量化器配置
        """
        if not config.validate():
            raise ValueError("Invalid configuration provided")
        
        self.config = config
        
        # 初始化OpenAI Embeddings
        kwargs = {
            "api_key": config.api_key,
            "model": config.model
        }
        
        if config.base_url:
            kwargs["base_url"] = config.base_url
            
        self.embeddings = OpenAIEmbeddings(**kwargs)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
    def vectorize_text(
        self, 
        text: str, 
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> List[float]:
        """
        对单个文本进行向量化（带重试机制）
        
        Args:
            text: 要向量化的文本
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            
        Returns:
            文本的向量表示
        """
        if not isinstance(text, str) or not text.strip():
            raise ValueError("Input must be a non-empty string")
        
        for attempt in range(max_retries + 1):
            try:
                return self.embeddings.embed_query(text)
            except Exception as e:
                if attempt == max_retries:
                    self.logger.error(f"Failed to vectorize text after {max_retries} retries: {e}")
                    raise
                
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
    
    def vectorize_documents_batch(
        self,
        documents: List[str],
        batch_size: Optional[int] = None,
        show_progress: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> List[List[float]]:
        """
        批量向量化文档（带进度显示和重试机制）
        
        Args:
            documents: 要向量化的文档列表
            batch_size: 批处理大小，如果为None则使用配置中的值
            show_progress: 是否显示进度条
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            
        Returns:
            文档向量列表
        """
        if not isinstance(documents, list) or not documents:
            raise ValueError("Documents must be a non-empty list of strings")
        
        # 验证所有文档
        for i, doc in enumerate(documents):
            if not isinstance(doc, str) or not doc.strip():
                raise ValueError(f"Document at index {i} must be a non-empty string")
        
        if batch_size is None:
            batch_size = self.config.batch_size
        
        all_vectors = []
        total_batches = (len(documents) + batch_size - 1) // batch_size
        
        # 创建进度条
        progress_bar = tqdm(
            total=total_batches,
            desc="Vectorizing documents",
            disable=not show_progress
        )
        
        try:
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                
                # 重试机制
                for attempt in range(max_retries + 1):
                    try:
                        batch_vectors = self.embeddings.embed_documents(batch)
                        all_vectors.extend(batch_vectors)
                        break
                    except Exception as e:
                        if attempt == max_retries:
                            self.logger.error(f"Failed to vectorize batch {i//batch_size + 1} after {max_retries} retries: {e}")
                            raise
                        
                        self.logger.warning(f"Batch {i//batch_size + 1} attempt {attempt + 1} failed: {e}. Retrying in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                        retry_delay *= 1.5  # 适度的指数退避
                
                progress_bar.update(1)
                
        finally:
            progress_bar.close()
        
        return all_vectors
    
    def vectorize(
        self, 
        content: Union[str, List[str]], 
        **kwargs
    ) -> Union[List[float], List[List[float]]]:
        """
        通用向量化方法
        
        Args:
            content: 要向量化的内容
            **kwargs: 传递给具体方法的参数
            
        Returns:
            向量或向量列表
        """
        if isinstance(content, str):
            return self.vectorize_text(content, **kwargs)
        elif isinstance(content, list):
            return self.vectorize_documents_batch(content, **kwargs)
        else:
            raise ValueError("Content must be a string or list of strings")
    
    def vectorize_with_callback(
        self,
        documents: List[str],
        callback: Callable[[int, List[List[float]]], None],
        batch_size: Optional[int] = None
    ) -> List[List[float]]:
        """
        带回调函数的向量化（用于实时处理结果）
        
        Args:
            documents: 要向量化的文档列表
            callback: 回调函数，接收(batch_index, batch_vectors)参数
            batch_size: 批处理大小
            
        Returns:
            所有向量列表
        """
        if batch_size is None:
            batch_size = self.config.batch_size
        
        all_vectors = []
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batch_vectors = self.embeddings.embed_documents(batch)
            all_vectors.extend(batch_vectors)
            
            # 调用回调函数
            callback(i // batch_size, batch_vectors)
        
        return all_vectors
    
    def get_dimension(self) -> int:
        """获取向量维度"""
        # 优先从配置获取
        dimension = self.config.get_model_dimension()
        if dimension:
            return dimension
        
        # 如果配置中没有，则通过测试获取
        test_vector = self.vectorize_text("test")
        return len(test_vector)
    
    def get_stats(self) -> dict:
        """获取向量化器统计信息"""
        return {
            "model": self.config.model,
            "dimension": self.get_dimension(),
            "batch_size": self.config.batch_size,
            "timeout": self.config.timeout,
            "base_url": self.config.base_url
        }


# 使用示例
if __name__ == "__main__":
    from vectorizer_config import VectorizerConfig
    
    # 创建配置
    config = VectorizerConfig.from_env()
    if not config.validate():
        config.api_key = "your-api-key-here"  # 替换为实际API密钥
    
    # 初始化增强版向量化器
    vectorizer = EnhancedVectorizer(config)
    
    # 测试单个文本
    text = "这是一个测试文本"
    vector = vectorizer.vectorize_text(text)
    print(f"单个文本向量维度: {len(vector)}")
    
    # 测试批量文档
    documents = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的核心技术",
        "深度学习是机器学习的一个子领域",
        "自然语言处理是AI的重要应用领域",
        "计算机视觉让机器能够理解图像"
    ]
    
    # 批量向量化（带进度条）
    vectors = vectorizer.vectorize_documents_batch(
        documents, 
        batch_size=2, 
        show_progress=True
    )
    print(f"批量向量化完成，共{len(vectors)}个向量")
    
    # 带回调函数的向量化
    def process_batch(batch_index, batch_vectors):
        print(f"处理完第{batch_index + 1}批，包含{len(batch_vectors)}个向量")
    
    vectors_with_callback = vectorizer.vectorize_with_callback(
        documents[:3], 
        callback=process_batch,
        batch_size=2
    )
    
    # 获取统计信息
    stats = vectorizer.get_stats()
    print(f"向量化器统计: {stats}")
