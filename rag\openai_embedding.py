from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from typing import List, Optional, Any
import requests
import json
import numpy as np

class CustomEmbeddings(OpenAIEmbeddings):
    """自定义Embedding模型调用类，支持model_id参数"""
    
    def __init__(
        self,
        api_url: str,
        api_key: str,
        model_id: str,  # 添加model_id参数
        headers: Optional[dict] = None,
        batch_size: int = 16,
        timeout: int = 60,
    ):
        self.api_url = api_url
        self.api_key = api_key
        self.model_id = model_id  # 存储model_id
        self.headers = headers or {}
        self.batch_size = batch_size
        self.timeout = timeout
        
        # 添加API认证头
        self.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        })
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """生成文档嵌入向量"""
        embeddings = []
        
        # 分批处理文本，避免请求过大
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i : i + self.batch_size]
            batch_embeddings = self._get_embeddings(batch)
            embeddings.extend(batch_embeddings)
        
        return embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """生成查询嵌入向量"""
        return self.embed_documents([text])[0]
    
    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """调用API获取嵌入向量"""
        try:
            # 根据实际API调整请求体格式，包含model_id
            payload = {
                "input": texts,
                "model": self.model_id  # 使用传入的model_id
            }
            payload_str = json.dumps(payload)
            payload_size = len(payload_str.encode('utf-8'))
            print(f"本次请求体大小: {payload_size} 字节, 批次文本数: {len(texts)}")
            # 假设API最大允许512KB
            max_size = 512 * 1024
            if payload_size > max_size and len(texts) > 1:
                print(f"请求体超出{max_size}字节，自动减小batch_size并重试。")
                half = max(1, len(texts) // 2)
                return self._get_embeddings(texts[:half]) + self._get_embeddings(texts[half:])
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=payload_str,
                timeout=self.timeout
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 根据实际API调整响应解析逻辑
            response_data = response.json()
            
            # 假设API返回格式为 {"data": [{"embedding": [0.1, 0.2, ...]}, ...]}
            if "data" in response_data:
                return [item["embedding"] for item in response_data["data"]]
            
            # 备用解析逻辑（根据实际API调整）
            return response_data.get("embeddings", [])
            
        except Exception as e:
            print(f"获取嵌入向量失败: {str(e)}")
            # 返回随机向量作为备选（实际应用中可替换为更合适的错误处理）
            return [[0.0] * 1024 for _ in range(len(texts))]

# 使用示例
if __name__ == "__main__":
    # 初始化自定义Embedding，指定model_id
    embeddings = CustomEmbeddings(
        api_url="https://api.siliconflow.cn/v1/embeddings",  # 替换为实际API URL
        api_key="sk-yasxyjkrvevjwtbvxsxyaulmxwtibwqoidvjfposfnvvuhhk",  # 替换为实际API Key
        model_id="BAAI/bge-m3",  # 替换为实际model_id
        batch_size=8  # 根据API限制调整批处理大小
    )
    
    # 测试文本嵌入
    texts = [
        "人工智能是一门研究领域",
        "大语言模型在自然语言处理中表现出色"
    ]
    
    # 获取文档嵌入
    doc_embeddings = embeddings.embed_documents(texts)
    print(f"文档嵌入数量: {len(doc_embeddings)}")
    print(f"第一个嵌入向量维度: {len(doc_embeddings[0])}")
    
    # 获取查询嵌入
    query_embedding = embeddings.embed_query("什么是机器学习？")
    print(f"查询嵌入向量维度: {len(query_embedding)}")