"""
简单的OpenAI Embeddings向量化工具
基于langchain_openai.OpenAIEmbeddings实现
支持从default_config.py获取配置
"""

from langchain_openai import OpenAIEmbeddings
from typing import List, Union, Optional
from .vectorizer_config import VectorizerConfig, get_config_with_fallback


class SimpleVectorizer:
    """
    基于OpenAI Embeddings的简单向量化工具
    支持文本和文档的向量化
    支持从default_config.py自动获取配置
    """

    def __init__(
        self,
        config: Optional[VectorizerConfig] = None,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        base_url: Optional[str] = None
    ):
        """
        初始化向量化器

        Args:
            config: VectorizerConfig配置对象，如果提供则优先使用
            api_key: API密钥，如果config为None时使用
            model: 模型名称，如果config为None时使用
            base_url: API基础URL，如果config为None时使用
        """
        # 如果没有提供config，则创建一个
        if config is None:
            if api_key or model or base_url:
                # 如果提供了单独的参数，使用这些参数创建配置
                config = VectorizerConfig()
                config.api_key = api_key
                config.model = model or "text-embedding-3-small"
                config.base_url = base_url
            else:
                # 否则使用默认配置（从default_config.py获取）
                config = get_config_with_fallback()

        # 验证配置
        if not config.validate():
            raise ValueError(
                "Invalid configuration. Please check your API key and model settings. "
                "You can set them in default_config.py or provide them as parameters."
            )

        self.config = config

        # 初始化OpenAI Embeddings
        kwargs = {
            "api_key": config.api_key,
            "model": config.model
        }

        if config.base_url:
            kwargs["base_url"] = config.base_url

        self.embeddings = OpenAIEmbeddings(**kwargs)
        self.model = config.model
    
    def vectorize_text(self, text: str) -> List[float]:
        """
        对单个文本进行向量化
        
        Args:
            text: 要向量化的文本
            
        Returns:
            文本的向量表示
        """
        if not isinstance(text, str):
            raise ValueError("Input must be a string")
        
        if not text.strip():
            raise ValueError("Input text cannot be empty")
            
        return self.embeddings.embed_query(text)
    
    def vectorize_documents(self, documents: List[str]) -> List[List[float]]:
        """
        对文档列表进行向量化
        
        Args:
            documents: 要向量化的文档列表
            
        Returns:
            文档向量列表
        """
        if not isinstance(documents, list):
            raise ValueError("Documents must be a list of strings")
        
        if not documents:
            raise ValueError("Documents list cannot be empty")
        
        # 检查所有文档都是字符串且非空
        for i, doc in enumerate(documents):
            if not isinstance(doc, str):
                raise ValueError(f"Document at index {i} must be a string")
            if not doc.strip():
                raise ValueError(f"Document at index {i} cannot be empty")
        
        return self.embeddings.embed_documents(documents)
    
    def vectorize(self, content: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        通用向量化方法，自动判断输入类型
        
        Args:
            content: 要向量化的内容，可以是单个字符串或字符串列表
            
        Returns:
            如果输入是字符串，返回单个向量
            如果输入是字符串列表，返回向量列表
        """
        if isinstance(content, str):
            return self.vectorize_text(content)
        elif isinstance(content, list):
            return self.vectorize_documents(content)
        else:
            raise ValueError("Content must be a string or list of strings")
    
    def get_dimension(self) -> int:
        """
        获取向量维度
        
        Returns:
            向量维度
        """
        # 使用简单测试文本获取维度
        test_vector = self.vectorize_text("test")
        return len(test_vector)
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            包含模型名称和维度的字典
        """
        return {
            "model": self.model,
            "dimension": self.get_dimension()
        }


# 使用示例
if __name__ == "__main__":
    print("=== SimpleVectorizer 使用示例 ===")

    # 方式1: 使用default_config.py中的配置（推荐）
    try:
        vectorizer = SimpleVectorizer()
        print("✓ 使用default_config.py配置初始化成功")
        print(f"模型: {vectorizer.config.model}")
        print(f"API URL: {vectorizer.config.base_url}")
    except ValueError as e:
        print(f"初始化失败: {e}")

        # 方式2: 手动提供配置
        print("尝试手动配置...")
        vectorizer = SimpleVectorizer(
            api_key="your-api-key-here",
            model="text-embedding-3-small",
            base_url="https://api.openai.com/v1"
        )
    
    # 测试单个文本向量化
    text = "这是一个测试文本"
    vector = vectorizer.vectorize_text(text)
    print(f"文本: {text}")
    print(f"向量维度: {len(vector)}")
    print(f"向量前5个值: {vector[:5]}")
    
    # 测试文档列表向量化
    documents = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的核心技术",
        "深度学习是机器学习的一个子领域"
    ]
    
    doc_vectors = vectorizer.vectorize_documents(documents)
    print(f"\n文档数量: {len(documents)}")
    print(f"向量数量: {len(doc_vectors)}")
    print(f"每个向量维度: {len(doc_vectors[0])}")
    
    # 测试通用向量化方法
    single_result = vectorizer.vectorize("单个文本测试")
    batch_result = vectorizer.vectorize(["文本1", "文本2"])
    
    print(f"\n单个文本结果类型: {type(single_result)}")
    print(f"批量文本结果类型: {type(batch_result)}")
    
    # 获取模型信息
    model_info = vectorizer.get_model_info()
    print(f"\n模型信息: {model_info}")
