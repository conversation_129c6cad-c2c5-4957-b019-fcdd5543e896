"""
简单的OpenAI Embeddings向量化工具
基于langchain_openai.OpenAIEmbeddings实现
"""

from langchain_openai import OpenAIEmbeddings
from typing import List, Union, Optional
import os


class SimpleVectorizer:
    """
    基于OpenAI Embeddings的简单向量化工具
    支持文本和文档的向量化
    """
    
    def __init__(
        self, 
        api_key: Optional[str] = None,
        model: str = "text-embedding-3-small",
        base_url: Optional[str] = None
    ):
        """
        初始化向量化器
        
        Args:
            api_key: OpenAI API密钥，如果为None则从环境变量OPENAI_API_KEY获取
            model: 使用的embedding模型，默认为text-embedding-3-small
            base_url: API基础URL，用于自定义API端点
        """
        # 如果没有提供api_key，尝试从环境变量获取
        if api_key is None:
            api_key = os.getenv("OPENAI_API_KEY")
            if api_key is None:
                raise ValueError("API key is required. Please provide api_key parameter or set OPENAI_API_KEY environment variable.")
        
        # 初始化OpenAI Embeddings
        kwargs = {
            "api_key": api_key,
            "model": model
        }
        
        if base_url:
            kwargs["base_url"] = base_url
            
        self.embeddings = OpenAIEmbeddings(**kwargs)
        self.model = model
    
    def vectorize_text(self, text: str) -> List[float]:
        """
        对单个文本进行向量化
        
        Args:
            text: 要向量化的文本
            
        Returns:
            文本的向量表示
        """
        if not isinstance(text, str):
            raise ValueError("Input must be a string")
        
        if not text.strip():
            raise ValueError("Input text cannot be empty")
            
        return self.embeddings.embed_query(text)
    
    def vectorize_documents(self, documents: List[str]) -> List[List[float]]:
        """
        对文档列表进行向量化
        
        Args:
            documents: 要向量化的文档列表
            
        Returns:
            文档向量列表
        """
        if not isinstance(documents, list):
            raise ValueError("Documents must be a list of strings")
        
        if not documents:
            raise ValueError("Documents list cannot be empty")
        
        # 检查所有文档都是字符串且非空
        for i, doc in enumerate(documents):
            if not isinstance(doc, str):
                raise ValueError(f"Document at index {i} must be a string")
            if not doc.strip():
                raise ValueError(f"Document at index {i} cannot be empty")
        
        return self.embeddings.embed_documents(documents)
    
    def vectorize(self, content: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        """
        通用向量化方法，自动判断输入类型
        
        Args:
            content: 要向量化的内容，可以是单个字符串或字符串列表
            
        Returns:
            如果输入是字符串，返回单个向量
            如果输入是字符串列表，返回向量列表
        """
        if isinstance(content, str):
            return self.vectorize_text(content)
        elif isinstance(content, list):
            return self.vectorize_documents(content)
        else:
            raise ValueError("Content must be a string or list of strings")
    
    def get_dimension(self) -> int:
        """
        获取向量维度
        
        Returns:
            向量维度
        """
        # 使用简单测试文本获取维度
        test_vector = self.vectorize_text("test")
        return len(test_vector)
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            包含模型名称和维度的字典
        """
        return {
            "model": self.model,
            "dimension": self.get_dimension()
        }


# 使用示例
if __name__ == "__main__":
    # 方式1: 使用环境变量中的API密钥
    try:
        vectorizer = SimpleVectorizer()
        print("使用环境变量API密钥初始化成功")
    except ValueError as e:
        print(f"初始化失败: {e}")
        # 方式2: 直接提供API密钥
        vectorizer = SimpleVectorizer(
            api_key="your-api-key-here",
            model="text-embedding-3-small"
        )
    
    # 测试单个文本向量化
    text = "这是一个测试文本"
    vector = vectorizer.vectorize_text(text)
    print(f"文本: {text}")
    print(f"向量维度: {len(vector)}")
    print(f"向量前5个值: {vector[:5]}")
    
    # 测试文档列表向量化
    documents = [
        "人工智能是计算机科学的一个分支",
        "机器学习是人工智能的核心技术",
        "深度学习是机器学习的一个子领域"
    ]
    
    doc_vectors = vectorizer.vectorize_documents(documents)
    print(f"\n文档数量: {len(documents)}")
    print(f"向量数量: {len(doc_vectors)}")
    print(f"每个向量维度: {len(doc_vectors[0])}")
    
    # 测试通用向量化方法
    single_result = vectorizer.vectorize("单个文本测试")
    batch_result = vectorizer.vectorize(["文本1", "文本2"])
    
    print(f"\n单个文本结果类型: {type(single_result)}")
    print(f"批量文本结果类型: {type(batch_result)}")
    
    # 获取模型信息
    model_info = vectorizer.get_model_info()
    print(f"\n模型信息: {model_info}")
