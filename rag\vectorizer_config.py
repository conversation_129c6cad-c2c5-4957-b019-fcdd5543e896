"""
向量化器配置管理
从default_config.py获取embedding相关配置
"""

import os
import sys
from typing import Optional, Dict, Any

# 添加项目根目录到路径，以便导入default_config
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from default_config import DEFAULT_CONFIG
except ImportError:
    DEFAULT_CONFIG = {}


class VectorizerConfig:
    """向量化器配置类，从default_config.py获取配置"""
    
    # 支持的OpenAI embedding模型及其维度
    SUPPORTED_MODELS = {
        "text-embedding-3-small": 1536,
        "text-embedding-3-large": 3072,
        "text-embedding-ada-002": 1536,
        "BAAI/bge-m3": 1024,  # 添加BGE模型支持
    }
    
    def __init__(self):
        self.api_key = None
        self.model = "text-embedding-3-small"
        self.base_url = None
        self.batch_size = 100
        self.timeout = 60
        
    @classmethod
    def from_default_config(cls) -> 'VectorizerConfig':
        """从default_config.py创建配置"""
        config = cls()
        
        # 从DEFAULT_CONFIG获取embedding相关配置
        config.api_key = DEFAULT_CONFIG.get("embedding_api_key")
        config.model = DEFAULT_CONFIG.get("embedding_llm", "text-embedding-3-small")
        config.base_url = DEFAULT_CONFIG.get("embedding_url")
        
        # 设置默认批处理大小（根据不同模型调整）
        if "bge" in config.model.lower():
            config.batch_size = 16  # BGE模型使用较小的批次
        else:
            config.batch_size = 100
            
        return config
    
    @classmethod
    def from_env(cls) -> 'VectorizerConfig':
        """从环境变量创建配置（保持兼容性）"""
        config = cls()
        config.api_key = os.getenv("OPENAI_API_KEY") or os.getenv("EMBEDDING_API_KEY")
        config.model = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
        config.base_url = os.getenv("OPENAI_BASE_URL") or os.getenv("EMBEDDING_URL")
        
        # 批处理大小
        batch_size = os.getenv("EMBEDDING_BATCH_SIZE")
        if batch_size:
            config.batch_size = int(batch_size)
            
        # 超时时间
        timeout = os.getenv("EMBEDDING_TIMEOUT")
        if timeout:
            config.timeout = int(timeout)
            
        return config
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'VectorizerConfig':
        """从字典创建配置"""
        config = cls()
        config.api_key = config_dict.get("api_key")
        config.model = config_dict.get("model", "text-embedding-3-small")
        config.base_url = config_dict.get("base_url")
        config.batch_size = config_dict.get("batch_size", 100)
        config.timeout = config_dict.get("timeout", 60)
        return config
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        if not self.api_key:
            return False
        if not self.model:
            return False
        return True
    
    def get_model_dimension(self) -> Optional[int]:
        """获取模型维度"""
        return self.SUPPORTED_MODELS.get(self.model)
    
    def is_openai_compatible(self) -> bool:
        """检查是否为OpenAI兼容的API"""
        openai_models = ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"]
        return self.model in openai_models
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "api_key": self.api_key,
            "model": self.model,
            "base_url": self.base_url,
            "batch_size": self.batch_size,
            "timeout": self.timeout
        }
    
    def __str__(self) -> str:
        """字符串表示（隐藏API密钥）"""
        masked_key = f"{self.api_key[:8]}..." if self.api_key else "None"
        return (f"VectorizerConfig(model={self.model}, "
                f"api_key={masked_key}, "
                f"base_url={self.base_url}, "
                f"batch_size={self.batch_size})")


# 便捷函数
def get_default_config() -> VectorizerConfig:
    """获取默认配置（从default_config.py）"""
    return VectorizerConfig.from_default_config()


def get_env_config() -> VectorizerConfig:
    """获取环境变量配置"""
    return VectorizerConfig.from_env()


def get_config_with_fallback() -> VectorizerConfig:
    """获取配置，优先使用default_config.py，回退到环境变量"""
    # 首先尝试从default_config获取
    config = VectorizerConfig.from_default_config()
    
    if config.validate():
        return config
    
    # 如果default_config无效，尝试环境变量
    env_config = VectorizerConfig.from_env()
    if env_config.validate():
        return env_config
    
    # 如果都无效，返回default_config（让用户知道需要配置）
    return config


# 使用示例
if __name__ == "__main__":
    print("=== 向量化器配置示例 ===")
    
    # 从default_config.py获取配置
    default_config = get_default_config()
    print(f"默认配置: {default_config}")
    print(f"配置有效性: {default_config.validate()}")
    
    if default_config.validate():
        print(f"模型维度: {default_config.get_model_dimension()}")
        print(f"OpenAI兼容: {default_config.is_openai_compatible()}")
    
    # 从环境变量获取配置
    env_config = get_env_config()
    print(f"\n环境变量配置: {env_config}")
    print(f"配置有效性: {env_config.validate()}")
    
    # 带回退的配置获取
    fallback_config = get_config_with_fallback()
    print(f"\n回退配置: {fallback_config}")
    print(f"配置有效性: {fallback_config.validate()}")
    
    # 显示DEFAULT_CONFIG中的embedding相关配置
    print(f"\n=== DEFAULT_CONFIG中的embedding配置 ===")
    embedding_keys = ["embedding_llm", "embedding_url", "embedding_api_key"]
    for key in embedding_keys:
        value = DEFAULT_CONFIG.get(key, "未设置")
        if "api_key" in key and value != "未设置":
            value = f"{value[:8]}..."  # 隐藏API密钥
        print(f"{key}: {value}")
