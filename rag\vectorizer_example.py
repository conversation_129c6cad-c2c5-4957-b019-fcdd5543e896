"""
向量化工具使用示例
演示如何使用从default_config.py获取配置的向量化器
"""

from simple_vectorizer import SimpleVectorizer
from vectorizer_config import VectorizerConfig, get_default_config, get_config_with_fallback


def example_with_default_config():
    """使用default_config.py配置的示例"""
    print("=== 使用default_config.py配置 ===")
    
    try:
        # 直接使用默认配置（从default_config.py获取）
        vectorizer = SimpleVectorizer()
        print("✓ 使用default_config.py配置初始化成功")
        
        # 显示配置信息
        config = vectorizer.config
        print(f"模型: {config.model}")
        print(f"API URL: {config.base_url}")
        print(f"批处理大小: {config.batch_size}")
        
        # 测试向量化
        test_text = "这是一个测试文本，使用default_config.py中的配置"
        vector = vectorizer.vectorize_text(test_text)
        print(f"文本: {test_text}")
        print(f"向量维度: {len(vector)}")
        print(f"向量前3个值: {vector[:3]}")
        
        return vectorizer
        
    except ValueError as e:
        print(f"❌ 初始化失败: {e}")
        print("请检查default_config.py中的embedding配置")
        return None


def example_batch_processing(vectorizer):
    """批量处理示例"""
    if vectorizer is None:
        print("跳过批量处理示例（向量化器未初始化）")
        return
    
    print("\n=== 批量处理示例 ===")
    
    # 测试文档
    documents = [
        "人工智能正在改变世界",
        "机器学习是AI的核心技术",
        "深度学习推动了AI的发展",
        "自然语言处理让机器理解人类语言",
        "计算机视觉让机器能够看懂图像"
    ]
    
    print(f"处理 {len(documents)} 个文档...")
    
    # 批量向量化
    vectors = vectorizer.vectorize_documents(documents)
    print(f"✓ 完成向量化，生成 {len(vectors)} 个向量")
    print(f"每个向量维度: {len(vectors[0])}")
    
    # 显示每个文档的向量信息
    for i, (doc, vector) in enumerate(zip(documents, vectors)):
        print(f"文档 {i+1}: {doc[:20]}... -> 向量维度: {len(vector)}")


def example_different_configs():
    """不同配置方式示例"""
    print("\n=== 不同配置方式示例 ===")
    
    # 1. 从default_config获取
    default_config = get_default_config()
    print(f"默认配置: {default_config}")
    print(f"配置有效性: {default_config.validate()}")
    
    # 2. 带回退的配置
    fallback_config = get_config_with_fallback()
    print(f"回退配置: {fallback_config}")
    
    # 3. 手动创建配置
    manual_config = VectorizerConfig.from_dict({
        "api_key": "sk-test123",
        "model": "BAAI/bge-m3",  # 使用BGE模型
        "base_url": "https://api.siliconflow.cn/v1",
        "batch_size": 16
    })
    print(f"手动配置: {manual_config}")
    print(f"模型维度: {manual_config.get_model_dimension()}")
    print(f"OpenAI兼容: {manual_config.is_openai_compatible()}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 测试无效配置
    try:
        invalid_config = VectorizerConfig()
        invalid_config.api_key = ""  # 空API密钥
        invalid_config.model = "invalid-model"
        
        vectorizer = SimpleVectorizer(config=invalid_config)
    except ValueError as e:
        print(f"✓ 正确捕获配置错误: {e}")
    
    # 测试无效输入
    try:
        vectorizer = SimpleVectorizer()
        vectorizer.vectorize_text("")  # 空文本
    except ValueError as e:
        print(f"✓ 正确捕获输入错误: {e}")
    except Exception:
        print("向量化器未正确初始化，跳过输入验证测试")


def show_default_config_info():
    """显示default_config.py中的embedding配置信息"""
    print("\n=== default_config.py 配置信息 ===")
    
    try:
        from default_config import DEFAULT_CONFIG
        
        embedding_keys = [
            "embedding_llm",
            "embedding_url", 
            "embedding_api_key"
        ]
        
        print("Embedding相关配置:")
        for key in embedding_keys:
            value = DEFAULT_CONFIG.get(key, "未设置")
            if "api_key" in key and value != "未设置":
                # 隐藏API密钥的大部分内容
                value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else f"{value[:4]}..."
            print(f"  {key}: {value}")
            
        # 检查配置完整性
        missing_keys = [key for key in embedding_keys if not DEFAULT_CONFIG.get(key)]
        if missing_keys:
            print(f"\n⚠ 缺少配置项: {missing_keys}")
        else:
            print("\n✓ 所有embedding配置项都已设置")
            
    except ImportError:
        print("❌ 无法导入default_config.py")


def main():
    """主函数"""
    print("OpenAI Embeddings 向量化工具使用示例")
    print("基于default_config.py配置")
    print("=" * 60)
    
    # 显示配置信息
    show_default_config_info()
    
    # 基本使用示例
    vectorizer = example_with_default_config()
    
    # 批量处理示例
    example_batch_processing(vectorizer)
    
    # 不同配置方式
    example_different_configs()
    
    # 错误处理
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("示例运行完成！")
    
    if vectorizer:
        print("✓ 向量化器工作正常")
    else:
        print("⚠ 向量化器初始化失败，请检查default_config.py中的配置")


if __name__ == "__main__":
    main()
