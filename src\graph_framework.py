# TradingAgents/graph/trading_graph.py

import os
from pathlib import Path
import json
from datetime import date
from typing import Dict, Any, Tuple, List, Optional

from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

from langgraph.prebuilt import <PERSON>l<PERSON>ode

from default_config import DEFAULT_CONFIG
from .graph_build import GraphSetup
from utils.config import set_config
from utils.state import InitialState
from utils.tools import Toolkit





class TradingAgentsGraph:
    """Main class that orchestrates the trading agents framework."""

    def __init__(
        self,
        debug=False,
        config: Dict[str, Any] = None,
    ):
        """Initialize the trading agents graph and components.

        Args:
            selected_analysts: List of analyst types to include
            debug: Whether to run in debug mode
            config: Configuration dictionary. If None, uses default config
        """
        self.debug = debug
        self.config = config or DEFAULT_CONFIG

        # Update the interface's config
        set_config(self.config)

        # Create necessary directories
        os.makedirs(
            os.path.join(self.config["project_dir"], "dataflows/data_cache"),
            exist_ok=True,
        )

        # Initialize LLMs
        if self.config["llm_provider"] == "openai":
            # OpenRouter支持：优先使用OPENROUTER_API_KEY，否则使用OPENAI_API_KEY
            self.deep_thinking_llm = ChatOpenAI(
                model=self.config["deep_think_llm"],
                base_url=self.config["backend_url"],
                api_key=self.config["api_key"]
            )
            self.quick_thinking_llm = ChatOpenAI(
                model=self.config["quick_think_llm"],
                base_url=self.config["backend_url"],
                api_key=self.config["api_key"]
            )
        elif self.config["llm_provider"] == "ollama":
            self.deep_thinking_llm = ChatOpenAI(model=self.config["deep_think_llm"], base_url=self.config["backend_url"])
            self.quick_thinking_llm = ChatOpenAI(model=self.config["quick_think_llm"], base_url=self.config["backend_url"])
        
        self.toolkit = Toolkit(config=self.config)



        # Create tool nodes
        self.tool_nodes = self._create_tool_nodes()

        # Initialize components
        # self.conditional_logic = nextStep()
        self.graph_setup = GraphSetup(
            self.quick_thinking_llm,
            self.deep_thinking_llm,
            self.toolkit,
            self.tool_nodes,
        )

        self.propagator = InitialState()


        # State tracking
        self.curr_state = None
        self.ticker = None
        self.log_states_dict = {}  # date to full state dict

        # Set up the graph
        self.graph = self.graph_setup.setup_graph()

    def _create_tool_nodes(self) -> Dict[str, ToolNode]:
        """Create tool nodes for different data sources."""
        return {
            # "news": ToolNode(
            #     [
            #         # online tools
            #         self.toolkit.search_data,
            #         # offline tools
            #         self.toolkit.get_sentiments,
            #         self.toolkit.get_news

            #     ]
            # ),
            # "fundamentals": ToolNode(
            #     [
            #         # online tools
            #         self.toolkit.search_data,
            #         # offline tools
            #         self.toolkit.get_fundamentals
            #     ]
            # ),
        }

    def build(self, text):
        """Run the trading agents graph for a company on a specific date."""

        # Initialize state
        init_agent_state = self.propagator.create_initial_state(
            text
        )
        args = self.propagator.get_graph_args()

        if self.debug:
            # Debug mode with tracing
            trace = []
            for chunk in self.graph.stream(init_agent_state, **args):
                if len(chunk["messages"]) == 0:
                    pass
                else:
                    last_message = chunk["messages"][-1]
                    # Filter out "Continue" placeholder messages
                    if hasattr(last_message, 'content') and last_message.content.strip() != "Continue" and not last_message.content.startswith("================================"):
                        last_message.pretty_print()
                    trace.append(chunk)

            final_state = trace[-1]
        else:
            # Standard mode without tracing
            final_state = self.graph.invoke(init_agent_state, **args)

        # Store current state for reflection
        self.curr_state = final_state

        # Log state
        # self._log_state(trade_date, final_state)

        # Return decision and processed signal
        # return final_state, self.process_signal(final_state["final_trade_decision"])
        return final_state

    def _log_state(self, trade_date, final_state):
        """Log the final state to a JSON file."""
        self.log_states_dict[str(trade_date)] = {
            "company_name": final_state["company_name"],
            "symbol":final_state["symbol"],
            "trade_date": final_state["trade_date"],
            "sentiment_report": final_state["sentiment_report"],
            "fundamentals_report": final_state["fundamentals_report"],
            
            "debate_research_state": {
                "bull_history": final_state["debate_research_state"]["bull_history"],
                "bear_history": final_state["debate_research_state"]["bear_history"],
                "history": final_state["debate_research_state"]["history"],
                "current_response": final_state["debate_research_state"][
                    "current_response"
                ],
                "judge_decision": final_state["debate_research_state"][
                    "judge_decision"
                ],
            },
            "investment_plan": final_state["investment_plan"],
        }

        # Save to file
        directory = Path(f"eval_results/{self.ticker}/TradingAgentsStrategy_logs/")
        directory.mkdir(parents=True, exist_ok=True)

        with open(
            f"eval_results/{self.ticker}/TradingAgentsStrategy_logs/full_states_log_{trade_date}.json",
            "w",
        ) as f:
            json.dump(self.log_states_dict, f, indent=4)

