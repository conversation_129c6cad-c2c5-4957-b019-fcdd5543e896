from trading.utils.state import AgentState

class nextStep:
    """Handles conditional logic for determining graph flow."""

    def __init__(self):
        """Initialize with configuration parameters."""


    def should_continue_news(self, state: AgentState):
        """Determine if news analysis should continue."""
        messages = state["messages"]
        last_message = messages[-1]
        if last_message.tool_calls:
            return "tools_news"
        return "Msg Clear News"

    def should_continue_fundamentals(self, state: AgentState):
        """Determine if fundamentals analysis should continue."""
        messages = state["messages"]
        last_message = messages[-1]
        if last_message.tool_calls:
            return "tools_fundamentals"
        return "Msg Clear Fundamentals"

    def should_continue_debate(self, state: AgentState) -> str:
        """Determine if debate should continue."""

        if (
            state["debate_research_state"]["count"] >= 2 * self.should_debate_count
        ):  # 3 rounds of back-and-forth between 2 agents
            return "Research Manager"
        if state["debate_research_state"]["current_response"].startswith("<PERSON>"):
            return "<PERSON> Researcher"
        return "Bull Researcher"


